#!/bin/bash

echo "=== 合并请求代码审查工具安装脚本 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1)
if [[ $? -eq 0 ]]; then
    echo "✓ 检测到Python: $python_version"
else
    echo "✗ 未检测到Python3，请先安装Python3"
    exit 1
fi

# 检查pip
pip_version=$(pip3 --version 2>&1)
if [[ $? -eq 0 ]]; then
    echo "✓ 检测到pip: $pip_version"
else
    echo "✗ 未检测到pip3，请先安装pip3"
    exit 1
fi

# 安装依赖
echo ""
echo "正在安装Python依赖包..."
pip3 install -r requirements.txt

if [[ $? -eq 0 ]]; then
    echo "✓ 依赖包安装成功"
else
    echo "✗ 依赖包安装失败"
    exit 1
fi

# 检查环境变量
echo ""
echo "检查环境变量配置..."

if [[ -z "$XM_LLM_API_KEY" ]]; then
    echo "⚠ 未设置 XM_LLM_API_KEY 环境变量，将使用默认值"
else
    echo "✓ XM_LLM_API_KEY 已设置"
fi

if [[ -z "$XM_LLM_BASE_URL" ]]; then
    echo "⚠ 未设置 XM_LLM_BASE_URL 环境变量，将使用默认值"
else
    echo "✓ XM_LLM_BASE_URL 已设置"
fi

if [[ -z "$XM_LLM_MODEL" ]]; then
    echo "⚠ 未设置 XM_LLM_MODEL 环境变量，将使用默认值"
else
    echo "✓ XM_LLM_MODEL 已设置"
fi

echo ""
echo "=== 安装完成 ==="
echo ""
echo "使用方法："
echo "  python3 mr_code_review.py"
echo ""
echo "示例URL："
echo "  https://codeup.aliyun.com/6189f099041d450d2c253abc/tech/xianmu-ai/change/255/diffs"
echo ""
echo "环境变量配置（可选）："
echo "  export XM_LLM_API_KEY=\"your-api-key\""
echo "  export XM_LLM_BASE_URL=\"https://test-one-api.summerfarm.top/v1\""
echo "  export XM_LLM_MODEL=\"deepseek-v3-250324\""
